import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  getAuthToken,
  getUserData,
  setAuthToken,
  setUserData,
  clearAuthCookies,
  setupAxiosInterceptors
} from '../utils/auth';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any;
  login: (token: string, userData: any) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<any>(null);

  // Debug: Monitor state changes
  useEffect(() => {
    console.log('AuthContext state changed - isAuthenticated:', isAuthenticated, 'user:', user);
  }, [isAuthenticated, user]);

  useEffect(() => {
    console.log('AuthContext initializing...');

    // Setup axios interceptors
    setupAxiosInterceptors();

    // Check if user is already logged in
    const token = getAuthToken();
    const userData = getUserData();

    console.log('Initial auth check - token:', token ? 'present' : 'missing', 'userData:', userData);

    if (token && userData) {
      console.log('Found existing auth data, setting authenticated state');
      setIsAuthenticated(true);
      setUser(userData);
    } else {
      console.log('No existing auth data found');
    }
  }, []);

  const login = (token: string, userData: any) => {
    console.log('AuthContext login called with:', { token: token ? 'present' : 'missing', userData });
    setAuthToken(token);
    setUserData(userData);
    setIsAuthenticated(true);
    setUser(userData);
    console.log('AuthContext state updated - isAuthenticated:', true, 'user:', userData);
  };

  const logout = () => {
    clearAuthCookies();
    setIsAuthenticated(false);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};