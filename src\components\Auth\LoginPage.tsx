import { Link, useNavigate } from 'react-router-dom'
import { useState } from 'react'
import axios from 'axios'
import { useAuth } from '../../context/AuthContext'

const LoginPage = () => {
  const navigate = useNavigate()
  const { login } = useAuth()
  const [formData, setFormData] = useState({
    loginField: '',
    password: ''
  })
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    // Clear error when user starts typing
    if (error) setError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    console.log('=== LOGIN ATTEMPT STARTED ===')

    // Try different endpoint paths - uncomment the correct one:
    // const apiUrl = `${import.meta.env.VITE_BASEURL}auth/login`        // Original
    const apiUrl = `${import.meta.env.VITE_BASEURL}users/verify-user`   // Try this since registration uses /users/
    // const apiUrl = `${import.meta.env.VITE_BASEURL}users/login`     // Alternative 1
    // const apiUrl = `${import.meta.env.VITE_BASEURL}api/auth/login`  // Alternative 2
    // const apiUrl = `${import.meta.env.VITE_BASEURL}login`           // Alternative 3

    // Try different request formats that backends commonly expect
    // const requestData = {
    //   loginField: formData.loginField,
    //   password: formData.password
    // }

    // Try this format - many backends expect 'email' field:
    const requestData = {
      email: formData.loginField,
      password: formData.password
    }

    // If email doesn't work, try username:
    // const requestData = {
    //   username: formData.loginField,
    //   password: formData.password
    // }

    // Other alternatives (uncomment if needed):
    // const requestData = {
    //   username: formData.loginField,
    //   password: formData.password
    // }
    //
    // const requestData = {
    //   identifier: formData.loginField,
    //   password: formData.password
    // }

    console.log('API URL:', apiUrl)
    console.log('Request data:', requestData)
    console.log('About to make API call...')

    try {
      // Create a new axios instance without interceptors for login
      const loginAxios = axios.create()
      console.log('Making API request...')
      const response = await loginAxios.post(apiUrl, requestData)
      console.log('API request completed successfully')
      console.log('Response status:', response.status)
      console.log('Full response data:', response.data)

      if (response.status === 200 || response.status === 201) {
        console.log('Status is', response.status, ', processing response...')

        // Check the actual structure of the response
        console.log('Response data keys:', Object.keys(response.data))

        const { token, userData } = response.data
        console.log('Extracted token:', token ? 'TOKEN PRESENT' : 'TOKEN MISSING')
        console.log('Extracted userData:', userData ? 'USER DATA PRESENT' : 'USER DATA MISSING')
        console.log('Token type:', typeof token)
        console.log('UserData type:', typeof userData)

        if (token && userData) {
          console.log('Both token and userData are present, calling login...')
          login(token, userData)
          console.log('Login function called successfully')

          // Add a small delay to ensure state updates
          setTimeout(() => {
            console.log('Timeout executed, navigating to home page...')
            navigate('/')
            console.log('Navigate function called')
          }, 100)
        } else {
          console.error('Missing token or userData in response')
          console.error('Token exists:', !!token)
          console.error('UserData exists:', !!userData)
          setError('Login response missing required data')
        }
      }
    } catch (error: any) {
      console.log('=== LOGIN ERROR OCCURRED ===')
      console.error('Full error object:', error)

      if (error.response) {
        console.error('Error response status:', error.response.status)
        console.error('Error response data:', error.response.data)
        console.error('Error response headers:', error.response.headers)

        const errorMessage = error.response.data?.message ||
                           error.response.data?.error ||
                           error.response.data?.msg ||
                           `Server error (${error.response.status})`
        setError(errorMessage)
      } else if (error.request) {
        console.error('Network error - no response received:', error.request)
        setError('Network error. Please check your connection and ensure the server is running.')
      } else {
        console.error('Unknown error:', error.message)
        setError('Something went wrong. Please try again.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Or{' '}
          <Link to="/register" className="font-medium text-blue-600 hover:text-blue-500">
            create a new account
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="loginField" className="block text-sm font-medium text-gray-700">
                Username, Email or Phone Number
              </label>
              <div className="mt-1">
                <input
                  id="loginField"
                  name="loginField"
                  type="text"
                  autoComplete="username"
                  required
                  value={formData.loginField}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Enter username, email or phone number"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <a href="#" className="font-medium text-blue-600 hover:text-blue-500">
                  Forgot your password?
                </a>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                  isLoading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                }`}
              >
                {isLoading ? 'Signing in...' : 'Sign in'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
