import axios from "axios";
import Cookies from "js-cookie";

// Cookie name for storing JWT
export const AUTH_COOKIE_NAME = "auth_token";
export const USER_COOKIE_NAME = "user_data";

// Read auth token from cookie
export const getAuthToken = (): string | null => {
  const token = Cookies.get(AUTH_COOKIE_NAME) || null;
  console.log('getAuthToken called, result:', token ? 'token found' : 'no token');
  return token;
};

// Read user data from cookie
export const getUserData = (): any => {
  const userData = Cookies.get(USER_COOKIE_NAME);
  const parsed = userData ? JSON.parse(userData) : null;
  console.log('getUserData called, result:', parsed ? 'user data found' : 'no user data');
  return parsed;
};

// Set auth token in cookie
export const setAuthToken = (token: string, expiresInDays = 7): void => {
  console.log('setAuthToken called with token:', token ? 'token provided' : 'no token');
  Cookies.set(AUTH_COOKIE_NAME, token, { expires: expiresInDays, path: "/" });
  console.log('Token cookie set');
};

// Set user data in cookie
export const setUserData = (userData: any, expiresInDays = 7): void => {
  console.log('setUserData called with userData:', userData);
  Cookies.set(USER_COOKIE_NAME, JSON.stringify(userData), {
    expires: expiresInDays,
    path: "/"
  });
  console.log('User data cookie set');
};

// Clear auth cookies
export const clearAuthCookies = (): void => {
  Cookies.remove(AUTH_COOKIE_NAME, { path: "/" });
  Cookies.remove(USER_COOKIE_NAME, { path: "/" });
};

// Setup axios interceptors
export const setupAxiosInterceptors = (): void => {
  // Request interceptor to add token to headers
  axios.interceptors.request.use(
    (config) => {
      const token = getAuthToken();
      if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor to handle token expiration
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        // Token expired or invalid
        clearAuthCookies();
        window.location.href = "/login";
      }
      return Promise.reject(error);
    }
  );
};