import axios from "axios";
import Cookies from "js-cookie";

// Cookie name for storing JWT
export const AUTH_COOKIE_NAME = "auth_token";
export const USER_COOKIE_NAME = "user_data";

// Read auth token from cookie
export const getAuthToken = (): string | null => {
  return Cookies.get(AUTH_COOKIE_NAME) || null;
};

// Read user data from cookie
export const getUserData = (): any => {
  const userData = Cookies.get(USER_COOKIE_NAME);
  return userData ? JSON.parse(userData) : null;
};

// Set auth token in cookie
export const setAuthToken = (token: string, expiresInDays = 7): void => {
  Cookies.set(AUTH_COOKIE_NAME, token, { expires: expiresInDays, path: "/" });
};

// Set user data in cookie
export const setUserData = (userData: any, expiresInDays = 7): void => {
  Cookies.set(USER_COOKIE_NAME, JSON.stringify(userData), { 
    expires: expiresInDays, 
    path: "/" 
  });
};

// Clear auth cookies
export const clearAuthCookies = (): void => {
  Cookies.remove(AUTH_COOKIE_NAME, { path: "/" });
  Cookies.remove(USER_COOKIE_NAME, { path: "/" });
};

// Setup axios interceptors
export const setupAxiosInterceptors = (): void => {
  // Request interceptor to add token to headers
  axios.interceptors.request.use(
    (config) => {
      const token = getAuthToken();
      if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor to handle token expiration
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        // Token expired or invalid
        clearAuthCookies();
        window.location.href = "/login";
      }
      return Promise.reject(error);
    }
  );
};